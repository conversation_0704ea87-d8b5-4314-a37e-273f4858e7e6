# 📖 دليل الاستخدام السريع - نظام تتبع حركة المخزون

## 🚀 البدء السريع

### الوصول للنظام
1. افتح النظام وسجل الدخول
2. اذهب إلى قسم **"إدارة المخزون"**
3. مرر لأسفل لترى **"سجل تتبع حركة المخزون الزمني"**

## 📊 فهم الجدول

### الأعمدة الموجودة:
- **التاريخ**: تاريخ العملية
- **الوقت**: وقت العملية بالدقيقة
- **كود المنتج**: الرمز الفريد للمنتج
- **اسم المنتج**: اسم المنتج كاملاً
- **نوع العملية**: 
  - 🟢 **إضافة** (باللون الأخضر)
  - 🔴 **سحب** (باللون الأحمر)  
  - 🟡 **سحب عامل** (باللون الأصفر)
- **الكمية السابقة**: الكمية قبل العملية
- **الكمية المضافة/المسحوبة**: مقدار التغيير
- **الكمية الحالية**: الكمية بعد العملية
- **المستخدم**: من قام بالعملية
- **ملاحظات**: تفاصيل إضافية
- **الإجراءات**: أزرار التحكم

## 🔍 البحث والفلترة

### 1. البحث بكود المنتج
- اكتب في حقل **"البحث بكود المنتج..."**
- يمكن البحث بكود المنتج أو اسمه
- النتائج تظهر فوراً أثناء الكتابة

### 2. الفلترة بالتاريخ
- **من تاريخ**: حدد تاريخ البداية
- **إلى تاريخ**: حدد تاريخ النهاية
- سيعرض فقط العمليات في هذه الفترة

### 3. فلترة نوع العملية
- اختر من القائمة:
  - **جميع العمليات** (افتراضي)
  - **إضافة فقط**
  - **سحب فقط** 
  - **سحب عمال**

### 4. مسح الفلاتر
- اضغط على **"مسح الفلاتر"** لإعادة تعيين كل شيء

## 🖨️ طباعة التقارير

### خطوات الطباعة:
1. **اختياري**: طبق الفلاتر المطلوبة أولاً
2. اضغط على **"طباعة التقرير"**
3. ستفتح نافذة جديدة بالتقرير
4. اضغط Ctrl+P أو استخدم قائمة الطباعة

### محتويات التقرير:
- عنوان المصنع وتاريخ الطباعة
- الفلاتر المطبقة (إن وجدت)
- جدول مفصل بجميع الحركات
- **ملخص إحصائي**:
  - إجمالي عدد الحركات
  - إجمالي الكميات المضافة
  - إجمالي الكميات المسحوبة
  - إجمالي سحب العمال
  - صافي الحركة

## 👁️ عرض التفاصيل

### لعرض تفاصيل حركة معينة:
1. اضغط على أيقونة **العين** 👁️ بجانب أي حركة
2. ستظهر نافذة بجميع التفاصيل:
   - معلومات العملية كاملة
   - تفاصيل المنتج
   - معلومات المستخدم
   - الملاحظات

## 💡 نصائح مهمة

### للحصول على أفضل النتائج:
- **استخدم البحث بالكود** للوصول السريع لمنتج معين
- **فلتر بالتاريخ** لمراجعة فترة زمنية محددة
- **اطبع التقارير بانتظام** للاحتفاظ بسجل ورقي
- **راجع الملخص الإحصائي** لفهم اتجاهات المخزون

### أمثلة عملية:

#### مثال 1: البحث عن منتج معين
```
1. اكتب "B001" في حقل البحث
2. ستظهر جميع حركات المنتج ذو الكود B001
```

#### مثال 2: مراجعة الأسبوع الماضي
```
1. حدد "من تاريخ": قبل أسبوع
2. حدد "إلى تاريخ": اليوم
3. اضغط "طباعة التقرير" للحصول على ملخص الأسبوع
```

#### مثال 3: مراجعة سحب العمال فقط
```
1. اختر "سحب عمال" من قائمة نوع العملية
2. ستظهر فقط عمليات سحب العمال
```

## ⚠️ ملاحظات مهمة

- **التحديث التلقائي**: الجدول يتحدث تلقائياً عند أي عملية مخزون جديدة
- **الترتيب**: الحركات مرتبة من الأحدث للأقدم
- **الألوان**: كل نوع عملية له لون مميز للتمييز السريع
- **الحفظ**: جميع البيانات محفوظة محلياً في المتصفح

## 🆘 حل المشاكل الشائعة

### لا تظهر بيانات في الجدول؟
- تأكد من وجود عمليات مخزون مسجلة
- جرب مسح الفلاتر
- أعد تحميل الصفحة

### البحث لا يعطي نتائج؟
- تأكد من كتابة كود المنتج بشكل صحيح
- جرب البحث باسم المنتج بدلاً من الكود
- تأكد من عدم وجود فلاتر أخرى مطبقة

### التقرير لا يطبع؟
- تأكد من السماح للنوافذ المنبثقة
- جرب متصفح آخر
- تأكد من وجود طابعة متصلة

---

**💬 للدعم الفني**: اتصل بالمطور أو راجع الدليل الشامل
