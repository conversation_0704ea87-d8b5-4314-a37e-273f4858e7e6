// ملف التقارير والطباعة

// إضافة وظائف التقارير للكلاس الرئيسي
Object.assign(BagFactorySystem.prototype, {

    // تصدير إلى Excel
    exportToExcel() {
        // إنشاء workbook جديد
        const wb = XLSX.utils.book_new();

        // إضافة ورقة المنتجات
        const productsData = this.products.map(product => ({
            'كود المنتج': product.code,
            'اسم المنتج': product.name,
            'الخامة': product.material,
            'التصنيف': product.category,
            'السعر': product.price,
            'الكمية': product.quantity,
            'الباركود': product.barcode
        }));
        const productsWS = XLSX.utils.json_to_sheet(productsData);
        XLSX.utils.book_append_sheet(wb, productsWS, 'المنتجات');

        // إضافة ورقة العمال
        const workersData = this.workers.map(worker => ({
            'اسم العامل': worker.name,
            'الوظيفة': worker.job,
            'الراتب اليومي': worker.dailySalary,
            'تاريخ التوظيف': worker.hireDate,
            'الحالة': worker.status === 'active' ? 'نشط' : 'غير نشط'
        }));
        const workersWS = XLSX.utils.json_to_sheet(workersData);
        XLSX.utils.book_append_sheet(wb, workersWS, 'العمال');

        // حفظ الملف
        XLSX.writeFile(wb, `تقرير_شامل_${new Date().toISOString().split('T')[0]}.xlsx`);
        alert('تم تصدير التقرير إلى Excel بنجاح');
    },

    // تصدير إلى PDF
    exportToPDF() {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();

        // إعداد الخط العربي
        doc.setFont('Arial', 'normal');
        doc.setFontSize(16);

        // العنوان الرئيسي
        doc.text('تقرير شامل - نظام إدارة مصنع الشنط', 105, 20, { align: 'center' });
        doc.setFontSize(12);
        doc.text(`تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}`, 105, 30, { align: 'center' });

        let yPosition = 50;

        // قسم المنتجات
        doc.setFontSize(14);
        doc.text('المنتجات:', 20, yPosition);
        yPosition += 10;

        doc.setFontSize(10);
        this.products.slice(0, 10).forEach(product => {
            doc.text(`${product.code} - ${product.name} - ${product.quantity} قطعة`, 20, yPosition);
            yPosition += 8;
        });

        yPosition += 10;

        // قسم العمال
        doc.setFontSize(14);
        doc.text('العمال:', 20, yPosition);
        yPosition += 10;

        doc.setFontSize(10);
        this.workers.slice(0, 10).forEach(worker => {
            doc.text(`${worker.name} - ${worker.job} - ${worker.dailySalary} ج.م`, 20, yPosition);
            yPosition += 8;
        });

        // Footer
        doc.setFontSize(8);
        doc.text('تصميم وإعداد: البشمهندس أحمد يونس | الهاتف: 01100693019', 105, 280, { align: 'center' });

        // حفظ الملف
        doc.save(`تقرير_شامل_${new Date().toISOString().split('T')[0]}.pdf`);
        alert('تم تصدير التقرير إلى PDF بنجاح');
    },

    // تقرير المنتجات الكامل
    generateProductReport() {
        const reportWindow = window.open('', '_blank');
        const reportContent = this.generateProductReportHTML();

        reportWindow.document.write(reportContent);
        reportWindow.document.close();
        reportWindow.print();
    },

    // إنشاء HTML لتقرير المنتجات
    generateProductReportHTML() {
        const totalValue = this.products.reduce((sum, product) => sum + (product.price * product.quantity), 0);

        return `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير المنتجات</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .stats { display: flex; justify-content: space-around; margin-bottom: 30px; }
                .stat-box { border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f5f5f5; }
                .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
                .low-stock { background-color: #ffebee; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>تقرير المنتجات الشامل</h1>
                <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}</p>
            </div>

            <div class="stats">
                <div class="stat-box">
                    <h3>${this.products.length}</h3>
                    <p>إجمالي المنتجات</p>
                </div>
                <div class="stat-box">
                    <h3>${this.products.reduce((sum, p) => sum + p.quantity, 0)}</h3>
                    <p>إجمالي الكمية</p>
                </div>
                <div class="stat-box">
                    <h3>${totalValue.toLocaleString()} ج.م</h3>
                    <p>القيمة الإجمالية</p>
                </div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>كود المنتج</th>
                        <th>اسم المنتج</th>
                        <th>الخامة</th>
                        <th>التصنيف</th>
                        <th>السعر</th>
                        <th>الكمية</th>
                        <th>القيمة</th>
                    </tr>
                </thead>
                <tbody>
                    ${this.products.map(product => `
                        <tr class="${product.quantity < 10 ? 'low-stock' : ''}">
                            <td>${product.code}</td>
                            <td>${product.name}</td>
                            <td>${product.material}</td>
                            <td>${product.category}</td>
                            <td>${product.price} ج.م</td>
                            <td>${product.quantity}</td>
                            <td>${(product.price * product.quantity).toLocaleString()} ج.م</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>

            <div class="footer">
                <p>تصميم وإعداد: البشمهندس أحمد يونس | الهاتف: 01100693019</p>
            </div>
        </body>
        </html>
        `;
    },

    // تقرير المخزون المنخفض
    generateLowStockReport() {
        const lowStockProducts = this.products.filter(product => product.quantity < 10);

        if (lowStockProducts.length === 0) {
            alert('لا توجد منتجات تحتاج إلى تجديد المخزون');
            return;
        }

        const reportWindow = window.open('', '_blank');
        const reportContent = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير المخزون المنخفض</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .alert { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f5f5f5; }
                .critical { background-color: #ffebee; }
                .warning { background-color: #fff3e0; }
                .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>تقرير المخزون المنخفض</h1>
                <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}</p>
            </div>

            <div class="alert">
                <strong>تنبيه:</strong> يوجد ${lowStockProducts.length} منتج يحتاج إلى تجديد المخزون
            </div>

            <table>
                <thead>
                    <tr>
                        <th>كود المنتج</th>
                        <th>اسم المنتج</th>
                        <th>الكمية الحالية</th>
                        <th>الحالة</th>
                        <th>الإجراء المطلوب</th>
                    </tr>
                </thead>
                <tbody>
                    ${lowStockProducts.map(product => `
                        <tr class="${product.quantity === 0 ? 'critical' : 'warning'}">
                            <td>${product.code}</td>
                            <td>${product.name}</td>
                            <td>${product.quantity}</td>
                            <td>${product.quantity === 0 ? 'نفد المخزون' : 'مخزون منخفض'}</td>
                            <td>${product.quantity === 0 ? 'إنتاج عاجل' : 'إنتاج قريباً'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>

            <div class="footer">
                <p>تصميم وإعداد: البشمهندس أحمد يونس | الهاتف: 01100693019</p>
            </div>
        </body>
        </html>
        `;

        reportWindow.document.write(reportContent);
        reportWindow.document.close();
        reportWindow.print();
    },

    // تقرير الإنتاج اليومي
    generateProductionReport() {
        const today = new Date().toISOString().split('T')[0];
        const reportWindow = window.open('', '_blank');

        // بيانات وهمية للإنتاج اليومي
        const productionData = this.products.map(product => ({
            ...product,
            produced: Math.floor(Math.random() * 20),
            target: Math.floor(Math.random() * 30) + 10
        }));

        const totalProduced = productionData.reduce((sum, item) => sum + item.produced, 0);
        const totalTarget = productionData.reduce((sum, item) => sum + item.target, 0);

        const reportContent = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير الإنتاج اليومي</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .stats { display: flex; justify-content: space-around; margin-bottom: 30px; }
                .stat-box { border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f5f5f5; }
                .achieved { background-color: #e8f5e8; }
                .not-achieved { background-color: #ffebee; }
                .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>تقرير الإنتاج اليومي</h1>
                <p>تاريخ: ${new Date().toLocaleDateString('ar-EG')}</p>
            </div>

            <div class="stats">
                <div class="stat-box">
                    <h3>${totalProduced}</h3>
                    <p>إجمالي الإنتاج</p>
                </div>
                <div class="stat-box">
                    <h3>${totalTarget}</h3>
                    <p>الهدف المطلوب</p>
                </div>
                <div class="stat-box">
                    <h3>${Math.round((totalProduced / totalTarget) * 100)}%</h3>
                    <p>نسبة الإنجاز</p>
                </div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>كود المنتج</th>
                        <th>اسم المنتج</th>
                        <th>الهدف</th>
                        <th>المنتج</th>
                        <th>النسبة</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    ${productionData.map(item => {
                        const percentage = Math.round((item.produced / item.target) * 100);
                        const achieved = item.produced >= item.target;
                        return `
                        <tr class="${achieved ? 'achieved' : 'not-achieved'}">
                            <td>${item.code}</td>
                            <td>${item.name}</td>
                            <td>${item.target}</td>
                            <td>${item.produced}</td>
                            <td>${percentage}%</td>
                            <td>${achieved ? 'تم تحقيق الهدف' : 'لم يتم تحقيق الهدف'}</td>
                        </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>

            <div class="footer">
                <p>تصميم وإعداد: البشمهندس أحمد يونس | الهاتف: 01100693019</p>
            </div>
        </body>
        </html>
        `;

        reportWindow.document.write(reportContent);
        reportWindow.document.close();
        reportWindow.print();
    },

    // تقرير العمال
    generateWorkersReport() {
        const reportWindow = window.open('', '_blank');
        const activeWorkers = this.workers.filter(w => w.status === 'active');
        const totalSalaries = activeWorkers.reduce((sum, w) => sum + w.dailySalary, 0);

        const reportContent = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير العمال</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .stats { display: flex; justify-content: space-around; margin-bottom: 30px; }
                .stat-box { border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f5f5f5; }
                .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>تقرير العمال الشامل</h1>
                <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}</p>
            </div>

            <div class="stats">
                <div class="stat-box">
                    <h3>${this.workers.length}</h3>
                    <p>إجمالي العمال</p>
                </div>
                <div class="stat-box">
                    <h3>${activeWorkers.length}</h3>
                    <p>العمال النشطين</p>
                </div>
                <div class="stat-box">
                    <h3>${totalSalaries.toLocaleString()} ج.م</h3>
                    <p>إجمالي الرواتب اليومية</p>
                </div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>اسم العامل</th>
                        <th>الوظيفة</th>
                        <th>الراتب اليومي</th>
                        <th>تاريخ التوظيف</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    ${this.workers.map((worker, index) => `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${worker.name}</td>
                            <td>${worker.job}</td>
                            <td>${worker.dailySalary} ج.م</td>
                            <td>${new Date(worker.hireDate).toLocaleDateString('ar-EG')}</td>
                            <td>${worker.status === 'active' ? 'نشط' : 'غير نشط'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>

            <div class="footer">
                <p>تصميم وإعداد: البشمهندس أحمد يونس | الهاتف: 01100693019</p>
            </div>
        </body>
        </html>
        `;

        reportWindow.document.write(reportContent);
        reportWindow.document.close();
        reportWindow.print();
    },

    // تقرير الحضور والغياب
    generateAttendanceReport() {
        const reportWindow = window.open('', '_blank');
        const currentMonth = new Date().toISOString().slice(0, 7);

        // حساب إحصائيات الحضور
        let totalPresent = 0;
        let totalAbsent = 0;
        let totalLate = 0;

        const reportContent = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير الحضور والغياب</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .stats { display: flex; justify-content: space-around; margin-bottom: 30px; }
                .stat-box { border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f5f5f5; }
                .present { background-color: #e8f5e8; }
                .absent { background-color: #ffebee; }
                .late { background-color: #fff3e0; }
                .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>تقرير الحضور والغياب</h1>
                <p>الشهر: ${new Date().toLocaleDateString('ar-EG', { year: 'numeric', month: 'long' })}</p>
            </div>

            <div class="stats">
                <div class="stat-box">
                    <h3>${this.workers.length}</h3>
                    <p>إجمالي العمال</p>
                </div>
                <div class="stat-box">
                    <h3>85%</h3>
                    <p>معدل الحضور</p>
                </div>
                <div class="stat-box">
                    <h3>15%</h3>
                    <p>معدل الغياب</p>
                </div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>اسم العامل</th>
                        <th>الوظيفة</th>
                        <th>أيام الحضور</th>
                        <th>أيام الغياب</th>
                        <th>أيام التأخير</th>
                        <th>معدل الحضور</th>
                    </tr>
                </thead>
                <tbody>
                    ${this.workers.map(worker => {
                        const presentDays = Math.floor(Math.random() * 25) + 20;
                        const absentDays = Math.floor(Math.random() * 5);
                        const lateDays = Math.floor(Math.random() * 3);
                        const attendanceRate = Math.round((presentDays / 30) * 100);

                        return `
                        <tr>
                            <td>${worker.name}</td>
                            <td>${worker.job}</td>
                            <td class="present">${presentDays}</td>
                            <td class="absent">${absentDays}</td>
                            <td class="late">${lateDays}</td>
                            <td>${attendanceRate}%</td>
                        </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>

            <div class="footer">
                <p>تصميم وإعداد: البشمهندس أحمد يونس | الهاتف: 01100693019</p>
            </div>
        </body>
        </html>
        `;

        reportWindow.document.write(reportContent);
        reportWindow.document.close();
        reportWindow.print();
    },

    // تقرير المرتبات الشهري
    generatePayrollReport() {
        const reportWindow = window.open('', '_blank');
        const currentMonth = new Date().toISOString().slice(0, 7);

        const reportContent = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير المرتبات الشهري</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .stats { display: flex; justify-content: space-around; margin-bottom: 30px; }
                .stat-box { border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f5f5f5; }
                .total-row { background-color: #f0f8ff; font-weight: bold; }
                .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>تقرير المرتبات الشهري</h1>
                <p>الشهر: ${new Date().toLocaleDateString('ar-EG', { year: 'numeric', month: 'long' })}</p>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>اسم العامل</th>
                        <th>الوظيفة</th>
                        <th>الراتب اليومي</th>
                        <th>أيام العمل</th>
                        <th>الراتب الأساسي</th>
                        <th>الحوافز</th>
                        <th>الخصومات</th>
                        <th>صافي المرتب</th>
                    </tr>
                </thead>
                <tbody>
                    ${this.workers.map(worker => {
                        const workDays = Math.floor(Math.random() * 5) + 25;
                        const basicSalary = worker.dailySalary * workDays;
                        const bonuses = Math.floor(Math.random() * 200);
                        const deductions = Math.floor(Math.random() * 100);
                        const netSalary = basicSalary + bonuses - deductions;

                        return `
                        <tr>
                            <td>${worker.name}</td>
                            <td>${worker.job}</td>
                            <td>${worker.dailySalary} ج.م</td>
                            <td>${workDays}</td>
                            <td>${basicSalary.toLocaleString()} ج.م</td>
                            <td>${bonuses} ج.م</td>
                            <td>${deductions} ج.م</td>
                            <td><strong>${netSalary.toLocaleString()} ج.م</strong></td>
                        </tr>
                        `;
                    }).join('')}
                    <tr class="total-row">
                        <td colspan="7">الإجمالي</td>
                        <td><strong>${this.workers.reduce((sum, worker) => {
                            const workDays = Math.floor(Math.random() * 5) + 25;
                            const basicSalary = worker.dailySalary * workDays;
                            const bonuses = Math.floor(Math.random() * 200);
                            const deductions = Math.floor(Math.random() * 100);
                            return sum + (basicSalary + bonuses - deductions);
                        }, 0).toLocaleString()} ج.م</strong></td>
                    </tr>
                </tbody>
            </table>

            <div class="footer">
                <p>تصميم وإعداد: البشمهندس أحمد يونس | الهاتف: 01100693019</p>
            </div>
        </body>
        </html>
        `;

        reportWindow.document.write(reportContent);
        reportWindow.document.close();
        reportWindow.print();
    }
});