<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التحديثات الجديدة</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .test-section { background: white; margin: 20px 0; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-button { background: #007bff; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .success { color: green; }
        .error { color: red; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 5px; }
        .feature-list { background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .feature-item { margin: 5px 0; }
    </style>
</head>
<body>
    <h1>🆕 اختبار التحديثات الجديدة - نظام إدارة مصنع الشنط</h1>
    
    <div class="test-section">
        <h2>📋 ملخص التحديثات المُضافة</h2>
        <div class="feature-list">
            <h3>🕐 تحديثات الحضور والانصراف:</h3>
            <div class="feature-item">✅ إضافة وقت الانصراف للعمال</div>
            <div class="feature-item">✅ حساب ساعات العمل تلقائياً</div>
            <div class="feature-item">✅ زر تسجيل انصراف الكل</div>
            <div class="feature-item">✅ إحصائيات يومية للحضور</div>
            <div class="feature-item">✅ طباعة حضور اليوم</div>
            <div class="feature-item">✅ طباعة حضور الأسبوع</div>
        </div>
        
        <div class="feature-list">
            <h3>💰 تحديثات المرتبات:</h3>
            <div class="feature-item">✅ حساب المرتبات اليومية</div>
            <div class="feature-item">✅ حساب المرتبات الأسبوعية</div>
            <div class="feature-item">✅ حساب المرتبات الشهرية</div>
            <div class="feature-item">✅ حساب المرتبات السنوية</div>
            <div class="feature-item">✅ زر إضافة الشهر القادم</div>
            <div class="feature-item">✅ زر إضافة السنة القادمة</div>
            <div class="feature-item">✅ إحصائيات شاملة للمرتبات</div>
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 اختبار وظائف الحضور والانصراف الجديدة</h2>
        <button class="test-button" onclick="testAttendanceFunctions()">اختبار وظائف الحضور</button>
        <div id="attendanceResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>💼 اختبار وظائف المرتبات المحسنة</h2>
        <button class="test-button" onclick="testPayrollFunctions()">اختبار وظائف المرتبات</button>
        <div id="payrollResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🖨️ اختبار وظائف الطباعة الجديدة</h2>
        <button class="test-button" onclick="testPrintFunctions()">اختبار وظائف الطباعة</button>
        <div id="printResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📊 اختبار شامل للتحديثات</h2>
        <button class="test-button" onclick="runAllNewTests()" style="background: #28a745;">تشغيل جميع اختبارات التحديثات</button>
        <div id="allNewTestsResult" class="result"></div>
    </div>

    <script>
        // قائمة الوظائف الجديدة المطلوب اختبارها
        const newFunctions = [
            // وظائف الحضور الجديدة
            'markAllLeave',
            'printDailyAttendance', 
            'printWeeklyAttendance',
            'updateArrivalTime',
            'updateLeaveTime',
            'markLeave',
            
            // وظائف المرتبات الجديدة
            'addNextMonth',
            'addNextYear'
        ];

        function testFunction(functionName, resultElementId) {
            const resultElement = document.getElementById(resultElementId);
            
            try {
                if (typeof window[functionName] === 'function') {
                    resultElement.innerHTML += `<span class="success">✅ الدالة ${functionName} موجودة وجاهزة</span><br>`;
                    return true;
                } else {
                    resultElement.innerHTML += `<span class="error">❌ الدالة ${functionName} غير موجودة</span><br>`;
                    return false;
                }
            } catch (error) {
                resultElement.innerHTML += `<span class="error">❌ خطأ في اختبار ${functionName}: ${error.message}</span><br>`;
                return false;
            }
        }

        function testAttendanceFunctions() {
            const resultElement = document.getElementById('attendanceResult');
            resultElement.innerHTML = '<h4>🔄 جاري اختبار وظائف الحضور...</h4>';
            
            const attendanceFunctions = [
                'markAllLeave',
                'printDailyAttendance', 
                'printWeeklyAttendance',
                'updateArrivalTime',
                'updateLeaveTime',
                'markLeave'
            ];

            let passed = 0;
            attendanceFunctions.forEach(func => {
                if (testFunction(func, 'attendanceResult')) passed++;
            });

            const percentage = Math.round((passed / attendanceFunctions.length) * 100);
            resultElement.innerHTML += `<hr><h4 class="${percentage === 100 ? 'success' : 'error'}">
                نتيجة اختبار الحضور: ${passed}/${attendanceFunctions.length} (${percentage}%)
            </h4>`;
        }

        function testPayrollFunctions() {
            const resultElement = document.getElementById('payrollResult');
            resultElement.innerHTML = '<h4>🔄 جاري اختبار وظائف المرتبات...</h4>';
            
            const payrollFunctions = [
                'addNextMonth',
                'addNextYear',
                'calculatePayroll'
            ];

            let passed = 0;
            payrollFunctions.forEach(func => {
                if (testFunction(func, 'payrollResult')) passed++;
            });

            const percentage = Math.round((passed / payrollFunctions.length) * 100);
            resultElement.innerHTML += `<hr><h4 class="${percentage === 100 ? 'success' : 'error'}">
                نتيجة اختبار المرتبات: ${passed}/${payrollFunctions.length} (${percentage}%)
            </h4>`;
        }

        function testPrintFunctions() {
            const resultElement = document.getElementById('printResult');
            resultElement.innerHTML = '<h4>🔄 جاري اختبار وظائف الطباعة...</h4>';
            
            const printFunctions = [
                'printDailyAttendance',
                'printWeeklyAttendance',
                'exportToExcel',
                'exportToPDF'
            ];

            let passed = 0;
            printFunctions.forEach(func => {
                if (testFunction(func, 'printResult')) passed++;
            });

            const percentage = Math.round((passed / printFunctions.length) * 100);
            resultElement.innerHTML += `<hr><h4 class="${percentage === 100 ? 'success' : 'error'}">
                نتيجة اختبار الطباعة: ${passed}/${printFunctions.length} (${percentage}%)
            </h4>`;
        }

        function runAllNewTests() {
            const resultElement = document.getElementById('allNewTestsResult');
            resultElement.innerHTML = '<h3>🔄 جاري تشغيل جميع اختبارات التحديثات...</h3>';
            
            let passedTests = 0;
            let totalTests = newFunctions.length;
            
            newFunctions.forEach(functionName => {
                if (typeof window[functionName] === 'function') {
                    passedTests++;
                    resultElement.innerHTML += `<span class="success">✅ ${functionName}</span><br>`;
                } else {
                    resultElement.innerHTML += `<span class="error">❌ ${functionName}</span><br>`;
                }
            });
            
            const percentage = Math.round((passedTests / totalTests) * 100);
            const status = percentage >= 90 ? 'success' : percentage >= 70 ? 'warning' : 'error';
            
            resultElement.innerHTML += `
                <hr>
                <h3 class="${status}">
                    📊 نتيجة اختبار التحديثات: ${passedTests}/${totalTests} (${percentage}%)
                </h3>
                <div class="feature-list">
                    ${percentage === 100 ? 
                        '<h4 style="color: green;">🎉 ممتاز! جميع التحديثات تعمل بشكل مثالي</h4>' +
                        '<p>✅ وقت الانصراف يعمل</p>' +
                        '<p>✅ طباعة الحضور تعمل</p>' +
                        '<p>✅ المرتبات المتعددة تعمل</p>' +
                        '<p>✅ إضافة الشهور والسنوات تعمل</p>' :
                        '<h4 style="color: red;">⚠️ بعض التحديثات تحتاج مراجعة</h4>'
                    }
                </div>
            `;
        }

        // تشغيل اختبار سريع عند تحميل الصفحة
        window.onload = function() {
            console.log('🧪 صفحة اختبار التحديثات الجديدة جاهزة');
            console.log('📋 الوظائف الجديدة:', newFunctions);
            
            // عرض معلومات التحديثات
            document.body.insertAdjacentHTML('afterbegin', `
                <div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                    <h3 style="color: #155724;">🎯 التحديثات المُضافة بنجاح:</h3>
                    <ul style="color: #155724;">
                        <li><strong>الحضور والانصراف:</strong> إضافة وقت الانصراف وحساب ساعات العمل</li>
                        <li><strong>طباعة الحضور:</strong> تقارير يومية وأسبوعية قابلة للطباعة</li>
                        <li><strong>المرتبات المتقدمة:</strong> حساب يومي، أسبوعي، شهري، وسنوي</li>
                        <li><strong>إدارة الفترات:</strong> إضافة الشهور والسنوات القادمة</li>
                        <li><strong>إحصائيات محسنة:</strong> عرض شامل لجميع البيانات</li>
                    </ul>
                </div>
            `);
        };
    </script>

    <footer style="text-align: center; margin-top: 50px; color: #666;">
        <p>تصميم وإعداد: البشمهندس أحمد يونس | الهاتف: 01100693019</p>
        <p style="font-size: 12px; color: #999;">تم إضافة جميع التحديثات المطلوبة بنجاح ✅</p>
    </footer>
</body>
</html>
