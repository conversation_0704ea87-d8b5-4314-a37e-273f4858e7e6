<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الصفحات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <div class="neumorphic-card">
                    <h3 class="mb-4">
                        <i class="bi bi-gear me-2"></i>
                        اختبار نظام الصفحات
                    </h3>
                    
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <button class="btn btn-primary neumorphic-btn w-100" onclick="addTestProducts()">
                                <i class="bi bi-plus-circle me-2"></i>
                                إضافة منتجات تجريبية (20 منتج)
                            </button>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-success neumorphic-btn w-100" onclick="addTestWithdrawals()">
                                <i class="bi bi-person-dash me-2"></i>
                                إضافة سجلات سحب تجريبية (15 سجل)
                            </button>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-info neumorphic-btn w-100" onclick="addTestMovements()">
                                <i class="bi bi-clock-history me-2"></i>
                                إضافة حركات مخزون تجريبية (25 حركة)
                            </button>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <button class="btn btn-warning neumorphic-btn w-100" onclick="clearAllData()">
                                <i class="bi bi-trash me-2"></i>
                                مسح جميع البيانات التجريبية
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button class="btn btn-secondary neumorphic-btn w-100" onclick="goToMainApp()">
                                <i class="bi bi-arrow-left me-2"></i>
                                العودة للتطبيق الرئيسي
                            </button>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>تعليمات الاختبار:</strong>
                        <ul class="mb-0 mt-2">
                            <li>اضغط على الأزرار أعلاه لإضافة بيانات تجريبية</li>
                            <li>انتقل للتطبيق الرئيسي لرؤية نظام الصفحات في العمل</li>
                            <li>ستظهر أزرار التنقل عندما تتجاوز البيانات 5 عناصر</li>
                            <li>يمكنك استخدام أزرار الانتقال السريع للصفحة الأولى والأخيرة</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/app.js"></script>
    <script>
        // إضافة منتجات تجريبية
        function addTestProducts() {
            const app = new BagFactorySystem();
            
            for (let i = 1; i <= 20; i++) {
                const product = {
                    id: 'test-product-' + i,
                    code: 'TP' + String(i).padStart(3, '0'),
                    name: 'منتج تجريبي ' + i,
                    material: 'خامة تجريبية',
                    category: 'تصنيف تجريبي',
                    price: Math.floor(Math.random() * 100) + 10,
                    quantity: Math.floor(Math.random() * 50) + 5,
                    minStock: 5,
                    lastProduction: new Date().toISOString().split('T')[0],
                    barcode: 'TEST' + i
                };
                
                app.products.push(product);
            }
            
            app.saveProducts();
            alert('تم إضافة 20 منتج تجريبي بنجاح!');
        }
        
        // إضافة سجلات سحب تجريبية
        function addTestWithdrawals() {
            const app = new BagFactorySystem();
            
            // إضافة عامل تجريبي إذا لم يكن موجوداً
            if (!app.workers.find(w => w.id === 'test-worker-1')) {
                app.workers.push({
                    id: 'test-worker-1',
                    name: 'عامل تجريبي',
                    job: 'عامل إنتاج',
                    phone: '01234567890',
                    address: 'عنوان تجريبي',
                    salary: 200,
                    status: 'active',
                    hireDate: new Date().toISOString().split('T')[0]
                });
                app.saveWorkers();
            }
            
            for (let i = 1; i <= 15; i++) {
                const withdrawal = {
                    id: 'test-withdrawal-' + i,
                    workerId: 'test-worker-1',
                    workerName: 'عامل تجريبي',
                    workerJob: 'عامل إنتاج',
                    productId: 'test-product-1',
                    productName: 'منتج تجريبي 1',
                    quantity: Math.floor(Math.random() * 5) + 1,
                    value: Math.floor(Math.random() * 100) + 20,
                    notes: 'سحب تجريبي رقم ' + i,
                    timestamp: new Date(Date.now() - (i * 24 * 60 * 60 * 1000)).toISOString(),
                    date: new Date(Date.now() - (i * 24 * 60 * 60 * 1000)).toISOString().split('T')[0],
                    time: new Date().toLocaleTimeString('ar-EG')
                };
                
                if (!app.workerWithdrawals) app.workerWithdrawals = [];
                app.workerWithdrawals.push(withdrawal);
            }
            
            app.saveWorkerWithdrawals();
            alert('تم إضافة 15 سجل سحب تجريبي بنجاح!');
        }
        
        // إضافة حركات مخزون تجريبية
        function addTestMovements() {
            const app = new BagFactorySystem();
            
            const operationTypes = ['add', 'remove', 'worker_withdrawal'];
            const operationNames = ['إضافة', 'سحب', 'سحب عامل'];
            
            for (let i = 1; i <= 25; i++) {
                const opIndex = Math.floor(Math.random() * 3);
                const movement = {
                    id: 'test-movement-' + i,
                    productId: 'test-product-1',
                    productCode: 'TP001',
                    productName: 'منتج تجريبي 1',
                    operationType: operationTypes[opIndex],
                    operationName: operationNames[opIndex],
                    previousQuantity: Math.floor(Math.random() * 50) + 10,
                    changedQuantity: Math.floor(Math.random() * 10) + 1,
                    currentQuantity: Math.floor(Math.random() * 60) + 5,
                    user: 'مستخدم تجريبي',
                    notes: 'حركة تجريبية رقم ' + i,
                    timestamp: new Date(Date.now() - (i * 2 * 60 * 60 * 1000)).toISOString(),
                    date: new Date(Date.now() - (i * 2 * 60 * 60 * 1000)).toISOString().split('T')[0],
                    time: new Date().toLocaleTimeString('ar-EG')
                };
                
                if (!app.inventoryMovements) app.inventoryMovements = [];
                app.inventoryMovements.push(movement);
            }
            
            app.saveInventoryMovements();
            alert('تم إضافة 25 حركة مخزون تجريبية بنجاح!');
        }
        
        // مسح جميع البيانات التجريبية
        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات التجريبية؟')) {
                localStorage.removeItem('bagFactory_products');
                localStorage.removeItem('bagFactory_workerWithdrawals');
                localStorage.removeItem('bagFactory_inventoryMovements');
                localStorage.removeItem('bagFactory_workers');
                alert('تم مسح جميع البيانات التجريبية بنجاح!');
            }
        }
        
        // العودة للتطبيق الرئيسي
        function goToMainApp() {
            window.location.href = 'index.html';
        }
    </script>
</body>
</html>
