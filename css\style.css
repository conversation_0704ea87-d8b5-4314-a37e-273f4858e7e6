/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
    text-align: right;
}

/* متغيرات الألوان النيومورفيك */
:root {
    --bg-primary: #e0e5ec;
    --bg-secondary: #f0f3f7;
    --shadow-light: #ffffff;
    --shadow-dark: #a3b1c6;
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --accent-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
}

/* شاشة تسجيل الدخول */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
    background: var(--bg-primary);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 
        20px 20px 60px var(--shadow-dark),
        -20px -20px 60px var(--shadow-light);
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.login-icon {
    font-size: 4rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.login-title {
    color: var(--text-primary);
    margin-bottom: 2rem;
    font-weight: 600;
}

/* عناصر النيومورفيك */
.neumorphic-input {
    background: var(--bg-primary);
    border: none;
    border-radius: 15px;
    padding: 15px 20px;
    box-shadow: 
        inset 8px 8px 16px var(--shadow-dark),
        inset -8px -8px 16px var(--shadow-light);
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.neumorphic-input:focus {
    outline: none;
    box-shadow: 
        inset 12px 12px 20px var(--shadow-dark),
        inset -12px -12px 20px var(--shadow-light);
}

.neumorphic-btn {
    background: var(--bg-primary);
    border: none;
    border-radius: 15px;
    padding: 15px 30px;
    box-shadow: 
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
    color: var(--text-primary);
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

.neumorphic-btn:hover {
    transform: translateY(-2px);
    box-shadow: 
        12px 12px 20px var(--shadow-dark),
        -12px -12px 20px var(--shadow-light);
}

.neumorphic-btn:active {
    transform: translateY(0);
    box-shadow: 
        inset 4px 4px 8px var(--shadow-dark),
        inset -4px -4px 8px var(--shadow-light);
}

.neumorphic-card {
    background: var(--bg-primary);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 
        15px 15px 30px var(--shadow-dark),
        -15px -15px 30px var(--shadow-light);
    border: none;
    margin-bottom: 2rem;
}

/* شريط التنقل */
.neumorphic-navbar {
    background: var(--bg-primary);
    box-shadow: 
        0 8px 16px var(--shadow-dark),
        0 -8px 16px var(--shadow-light);
    padding: 1rem 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.navbar-brand {
    color: var(--text-primary) !important;
    font-weight: 700;
    font-size: 1.5rem;
}

/* القائمة الجانبية */
.sidebar {
    position: fixed;
    top: 80px;
    right: 0;
    width: 280px;
    height: calc(100vh - 80px);
    background: var(--bg-primary);
    box-shadow: 
        -8px 0 16px var(--shadow-dark),
        8px 0 16px var(--shadow-light);
    padding: 2rem 0;
    overflow-y: auto;
}

.sidebar-menu {
    padding: 0 1rem;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    margin-bottom: 0.5rem;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-primary);
    font-weight: 500;
}

.menu-item:hover {
    background: var(--bg-secondary);
    box-shadow: 
        inset 4px 4px 8px var(--shadow-dark),
        inset -4px -4px 8px var(--shadow-light);
}

.menu-item.active {
    background: var(--accent-color);
    color: white;
    box-shadow: 
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
}

.menu-item i {
    font-size: 1.2rem;
    margin-left: 1rem;
    width: 20px;
}

/* المحتوى الرئيسي */
.main-content {
    margin-right: 280px;
    margin-top: 80px;
    padding: 2rem;
    min-height: calc(100vh - 80px);
    background: var(--bg-secondary);
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--bg-primary);
}

.page-header h1 {
    color: var(--text-primary);
    font-weight: 600;
    display: flex;
    align-items: center;
}

/* بطاقات الإحصائيات */
.stat-card {
    display: flex;
    align-items: center;
    padding: 2rem;
    border-radius: 20px;
    background: var(--bg-primary);
    box-shadow: 
        15px 15px 30px var(--shadow-dark),
        -15px -15px 30px var(--shadow-light);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
    box-shadow: 
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
}

.stat-icon i {
    font-size: 1.5rem;
    color: white;
}

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.stat-info p {
    color: var(--text-secondary);
    margin: 0;
    font-weight: 500;
}

/* ألوان الخلفية للأيقونات */
.bg-primary { background: var(--accent-color); }
.bg-success { background: var(--success-color); }
.bg-info { background: var(--info-color); }
.bg-warning { background: var(--warning-color); }
.bg-danger { background: var(--danger-color); }

/* مربع البحث */
.search-box {
    position: relative;
}

.search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

/* الجداول */
.table {
    background: transparent;
    color: var(--text-primary);
}

.table th {
    background: var(--bg-primary);
    color: var(--text-primary);
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.table td {
    padding: 1rem;
    border-color: var(--bg-primary);
}

.table-hover tbody tr:hover {
    background: var(--bg-secondary);
}

/* Footer */
.footer {
    background: var(--bg-primary);
    padding: 1rem 0;
    margin-top: 2rem;
    box-shadow: 
        0 -8px 16px var(--shadow-dark),
        0 8px 16px var(--shadow-light);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
        padding: 1rem;
    }
    
    .login-card {
        margin: 1rem;
        padding: 2rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .page-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

/* تحسينات إضافية */
.form-label {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.btn-outline-danger {
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.btn-outline-danger:hover {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.navbar-text {
    color: var(--text-primary) !important;
}

/* تأثيرات الحركة */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.content-section.active {
    animation: fadeIn 0.5s ease;
}

/* تحسين شكل الـ Select */
.form-select {
    background: var(--bg-primary);
    border: none;
    border-radius: 15px;
    padding: 15px 20px;
    box-shadow:
        inset 8px 8px 16px var(--shadow-dark),
        inset -8px -8px 16px var(--shadow-light);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.form-select:focus {
    outline: none;
    box-shadow:
        inset 12px 12px 20px var(--shadow-dark),
        inset -12px -12px 20px var(--shadow-light);
}

/* تحسين الأزرار الصغيرة */
.btn-sm {
    padding: 0.5rem 1rem;
    border-radius: 10px;
    font-size: 0.875rem;
}

/* تحسين البطاقات */
.card-header {
    background: var(--bg-secondary);
    border: none;
    border-radius: 20px 20px 0 0;
    padding: 1.5rem;
    color: var(--text-primary);
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* تحسين الـ Badges */
.badge {
    padding: 0.5rem 1rem;
    border-radius: 10px;
    font-weight: 500;
}

/* تحسين النوافذ المنبثقة */
.modal-content {
    border: none;
    border-radius: 20px;
}

.modal-header {
    border: none;
    padding: 2rem 2rem 1rem;
}

.modal-body {
    padding: 1rem 2rem;
}

.modal-footer {
    border: none;
    padding: 1rem 2rem 2rem;
}

/* تحسين الجداول */
.table-responsive {
    border-radius: 15px;
    overflow: hidden;
    box-shadow:
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
}

/* تحسين أزرار الإجراءات */
.btn-outline-primary {
    border-color: var(--accent-color);
    color: var(--accent-color);
}

.btn-outline-primary:hover {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.btn-outline-success {
    border-color: var(--success-color);
    color: var(--success-color);
}

.btn-outline-success:hover {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-outline-warning {
    border-color: var(--warning-color);
    color: var(--warning-color);
}

.btn-outline-warning:hover {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}
        inset -8px -8px 16px var(--shadow-light);
    color: var(--text-primary);
}

.form-select:focus {
    outline: none;
    box-shadow:
        inset 12px 12px 20px var(--shadow-dark),
        inset -12px -12px 20px var(--shadow-light);
}

/* تحسينات إضافية للجداول */
.table-responsive {
    border-radius: 15px;
    overflow: hidden;
    box-shadow:
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
}

/* تحسين شكل الـ Badge */
.badge {
    border-radius: 10px;
    padding: 0.5em 0.8em;
    font-weight: 500;
}

/* تحسين الأزرار الصغيرة */
.btn-sm {
    border-radius: 8px;
    padding: 0.4rem 0.8rem;
}

/* تحسين النوافذ المنبثقة */
.modal-content {
    border: none;
    border-radius: 20px;
}

.modal-header {
    border-bottom: 2px solid var(--bg-secondary);
    border-radius: 20px 20px 0 0;
}

.modal-footer {
    border-top: 2px solid var(--bg-secondary);
    border-radius: 0 0 20px 20px;
}

/* تحسين الـ Input Group */
.input-group .form-control {
    border-radius: 15px 0 0 15px;
}

.input-group .btn {
    border-radius: 0 15px 15px 0;
    border: none;
    background: var(--bg-primary);
    box-shadow:
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
}

/* تحسين الـ Form Check */
.form-check-input {
    border-radius: 8px;
    border: none;
    box-shadow:
        inset 4px 4px 8px var(--shadow-dark),
        inset -4px -4px 8px var(--shadow-light);
}

.form-check-input:checked {
    background-color: var(--accent-color);
    box-shadow:
        inset 4px 4px 8px rgba(52, 152, 219, 0.3),
        inset -4px -4px 8px rgba(52, 152, 219, 0.1);
}

/* تحسين الـ Alert */
.alert {
    border: none;
    border-radius: 15px;
    box-shadow:
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
}

/* تحسين الـ Progress Bar */
.progress {
    border-radius: 10px;
    background: var(--bg-primary);
    box-shadow:
        inset 4px 4px 8px var(--shadow-dark),
        inset -4px -4px 8px var(--shadow-light);
}

.progress-bar {
    border-radius: 10px;
}

/* تحسين الـ Card Header */
.card-header {
    background: transparent;
    border-bottom: 2px solid var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
}

/* تأثيرات الحركة المحسنة */
.menu-item, .stat-card, .neumorphic-btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-item:hover {
    transform: translateX(-5px);
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow:
        20px 20px 40px var(--shadow-dark),
        -20px -20px 40px var(--shadow-light);
}

/* تحسين الـ Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-primary);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--accent-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #2980b9;
}

/* تحسين الطباعة */
@media print {
    .sidebar, .navbar, .footer, .btn, .modal {
        display: none !important;
    }

    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }

    .neumorphic-card {
        box-shadow: none !important;
        border: 1px solid #ddd;
    }
}
