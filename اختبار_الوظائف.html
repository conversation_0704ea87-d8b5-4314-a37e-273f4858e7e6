<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار وظائف النظام</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .test-section { background: white; margin: 20px 0; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-button { background: #007bff; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .success { color: green; }
        .error { color: red; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🧪 اختبار وظائف نظام إدارة مصنع الشنط</h1>
    
    <div class="test-section">
        <h2>🔐 اختبار وظائف تسجيل الدخول</h2>
        <button class="test-button" onclick="testLogin()">اختبار تسجيل الدخول</button>
        <button class="test-button" onclick="testLogout()">اختبار تسجيل الخروج</button>
        <div id="loginResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📦 اختبار وظائف المنتجات</h2>
        <button class="test-button" onclick="testShowAddProductModal()">اختبار نافذة إضافة منتج</button>
        <button class="test-button" onclick="testGenerateBarcode()">اختبار توليد باركود</button>
        <div id="productResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📊 اختبار وظائف المخزون</h2>
        <button class="test-button" onclick="testShowAddStockModal()">اختبار نافذة إضافة مخزون</button>
        <button class="test-button" onclick="testProcessStockOperation()">اختبار عملية المخزون</button>
        <div id="stockResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>👥 اختبار وظائف العمال</h2>
        <button class="test-button" onclick="testShowAddWorkerModal()">اختبار نافذة إضافة عامل</button>
        <button class="test-button" onclick="testMarkAllPresent()">اختبار تسجيل حضور الكل</button>
        <div id="workerResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>💰 اختبار وظائف المرتبات</h2>
        <button class="test-button" onclick="testCalculatePayroll()">اختبار حساب المرتبات</button>
        <div id="payrollResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📈 اختبار وظائف التقارير</h2>
        <button class="test-button" onclick="testExportToExcel()">اختبار تصدير Excel</button>
        <button class="test-button" onclick="testExportToPDF()">اختبار تصدير PDF</button>
        <button class="test-button" onclick="testGenerateProductReport()">اختبار تقرير المنتجات</button>
        <div id="reportResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>⚙️ اختبار وظائف الإعدادات</h2>
        <button class="test-button" onclick="testShowAddUserModal()">اختبار نافذة إضافة مستخدم</button>
        <button class="test-button" onclick="testExportData()">اختبار تصدير البيانات</button>
        <div id="settingsResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📋 نتائج الاختبار الشامل</h2>
        <button class="test-button" onclick="runAllTests()" style="background: #28a745;">تشغيل جميع الاختبارات</button>
        <div id="allTestsResult" class="result"></div>
    </div>

    <script>
        // قائمة الوظائف المطلوب اختبارها
        const functionsToTest = [
            'logout', 'showSection', 'showAddProductModal', 'showAddWorkerModal', 
            'showAddUserModal', 'showAddStockModal', 'addProduct', 'generateBarcode',
            'addWorker', 'addUser', 'markAllPresent', 'calculatePayroll',
            'exportToExcel', 'exportToPDF', 'generateProductReport',
            'generateLowStockReport', 'generateProductionReport', 'generateWorkersReport',
            'generateAttendanceReport', 'generatePayrollReport', 'exportData', 'importData',
            'processStockOperation', 'addStock', 'removeStock', 'printPayslip'
        ];

        function testFunction(functionName, resultElementId) {
            const resultElement = document.getElementById(resultElementId);
            
            try {
                if (typeof window[functionName] === 'function') {
                    resultElement.innerHTML += `<span class="success">✅ الدالة ${functionName} موجودة</span><br>`;
                    return true;
                } else {
                    resultElement.innerHTML += `<span class="error">❌ الدالة ${functionName} غير موجودة</span><br>`;
                    return false;
                }
            } catch (error) {
                resultElement.innerHTML += `<span class="error">❌ خطأ في اختبار ${functionName}: ${error.message}</span><br>`;
                return false;
            }
        }

        function testLogin() {
            testFunction('logout', 'loginResult');
        }

        function testLogout() {
            testFunction('showSection', 'loginResult');
        }

        function testShowAddProductModal() {
            testFunction('showAddProductModal', 'productResult');
            testFunction('addProduct', 'productResult');
        }

        function testGenerateBarcode() {
            testFunction('generateBarcode', 'productResult');
        }

        function testShowAddStockModal() {
            testFunction('showAddStockModal', 'stockResult');
        }

        function testProcessStockOperation() {
            testFunction('processStockOperation', 'stockResult');
            testFunction('addStock', 'stockResult');
            testFunction('removeStock', 'stockResult');
        }

        function testShowAddWorkerModal() {
            testFunction('showAddWorkerModal', 'workerResult');
            testFunction('addWorker', 'workerResult');
        }

        function testMarkAllPresent() {
            testFunction('markAllPresent', 'workerResult');
        }

        function testCalculatePayroll() {
            testFunction('calculatePayroll', 'payrollResult');
            testFunction('printPayslip', 'payrollResult');
        }

        function testExportToExcel() {
            testFunction('exportToExcel', 'reportResult');
        }

        function testExportToPDF() {
            testFunction('exportToPDF', 'reportResult');
        }

        function testGenerateProductReport() {
            testFunction('generateProductReport', 'reportResult');
            testFunction('generateLowStockReport', 'reportResult');
            testFunction('generateProductionReport', 'reportResult');
            testFunction('generateWorkersReport', 'reportResult');
            testFunction('generateAttendanceReport', 'reportResult');
            testFunction('generatePayrollReport', 'reportResult');
        }

        function testShowAddUserModal() {
            testFunction('showAddUserModal', 'settingsResult');
            testFunction('addUser', 'settingsResult');
        }

        function testExportData() {
            testFunction('exportData', 'settingsResult');
            testFunction('importData', 'settingsResult');
        }

        function runAllTests() {
            const resultElement = document.getElementById('allTestsResult');
            resultElement.innerHTML = '<h3>🔄 جاري تشغيل جميع الاختبارات...</h3>';
            
            let passedTests = 0;
            let totalTests = functionsToTest.length;
            
            functionsToTest.forEach(functionName => {
                if (typeof window[functionName] === 'function') {
                    passedTests++;
                    resultElement.innerHTML += `<span class="success">✅ ${functionName}</span><br>`;
                } else {
                    resultElement.innerHTML += `<span class="error">❌ ${functionName}</span><br>`;
                }
            });
            
            const percentage = Math.round((passedTests / totalTests) * 100);
            const status = percentage >= 90 ? 'success' : percentage >= 70 ? 'warning' : 'error';
            
            resultElement.innerHTML += `
                <hr>
                <h3 class="${status}">
                    📊 نتيجة الاختبار: ${passedTests}/${totalTests} (${percentage}%)
                </h3>
                <p>
                    ${percentage >= 90 ? '🎉 ممتاز! جميع الوظائف تعمل بشكل صحيح' : 
                      percentage >= 70 ? '⚠️ جيد، لكن هناك بعض الوظائف تحتاج إصلاح' : 
                      '🚨 يحتاج النظام إلى إصلاحات كبيرة'}
                </p>
            `;
        }

        // تشغيل اختبار سريع عند تحميل الصفحة
        window.onload = function() {
            console.log('🧪 صفحة اختبار النظام جاهزة');
            console.log('📋 الوظائف المطلوب اختبارها:', functionsToTest);
        };
    </script>

    <footer style="text-align: center; margin-top: 50px; color: #666;">
        <p>تصميم وإعداد: البشمهندس أحمد يونس | الهاتف: 01100693019</p>
    </footer>
</body>
</html>
