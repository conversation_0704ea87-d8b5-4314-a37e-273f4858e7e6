<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل لجميع الوظائف - مصنع الشنط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            background: #f8f9fa;
        }
        .test-button {
            margin: 5px;
            padding: 10px 20px;
            border: none;
            border-radius: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 10px;
            background: white;
            border: 1px solid #dee2e6;
            min-height: 50px;
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .test-stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">
            <i class="bi bi-gear-fill me-2"></i>
            اختبار شامل لجميع وظائف نظام إدارة مصنع الشنط
        </h1>
        
        <div class="test-stats">
            <div class="stat-item">
                <span class="stat-number" id="totalTests">0</span>
                <span>إجمالي الاختبارات</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="passedTests">0</span>
                <span>نجح</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="failedTests">0</span>
                <span>فشل</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="successRate">0%</span>
                <span>معدل النجاح</span>
            </div>
        </div>

        <div class="text-center mb-4">
            <button class="test-button btn-lg" onclick="runAllTests()">
                <i class="bi bi-play-fill me-2"></i>تشغيل جميع الاختبارات
            </button>
            <button class="test-button btn-lg" onclick="clearResults()">
                <i class="bi bi-trash me-2"></i>مسح النتائج
            </button>
        </div>

        <!-- اختبار الوظائف الأساسية -->
        <div class="test-section">
            <h2><i class="bi bi-gear me-2"></i>الوظائف الأساسية</h2>
            <button class="test-button" onclick="testBasicFunctions()">اختبار الوظائف الأساسية</button>
            <div id="basicResult" class="result"></div>
        </div>

        <!-- اختبار وظائف المنتجات -->
        <div class="test-section">
            <h2><i class="bi bi-bag me-2"></i>وظائف المنتجات</h2>
            <button class="test-button" onclick="testProductFunctions()">اختبار وظائف المنتجات</button>
            <div id="productResult" class="result"></div>
        </div>

        <!-- اختبار وظائف العمال -->
        <div class="test-section">
            <h2><i class="bi bi-people me-2"></i>وظائف العمال</h2>
            <button class="test-button" onclick="testWorkerFunctions()">اختبار وظائف العمال</button>
            <div id="workerResult" class="result"></div>
        </div>

        <!-- اختبار وظائف الحضور -->
        <div class="test-section">
            <h2><i class="bi bi-calendar-check me-2"></i>وظائف الحضور والانصراف</h2>
            <button class="test-button" onclick="testAttendanceFunctions()">اختبار وظائف الحضور</button>
            <div id="attendanceResult" class="result"></div>
        </div>

        <!-- اختبار وظائف المرتبات -->
        <div class="test-section">
            <h2><i class="bi bi-cash me-2"></i>وظائف المرتبات</h2>
            <button class="test-button" onclick="testPayrollFunctions()">اختبار وظائف المرتبات</button>
            <div id="payrollResult" class="result"></div>
        </div>

        <!-- اختبار وظائف المخزون -->
        <div class="test-section">
            <h2><i class="bi bi-boxes me-2"></i>وظائف المخزون</h2>
            <button class="test-button" onclick="testInventoryFunctions()">اختبار وظائف المخزون</button>
            <div id="inventoryResult" class="result"></div>
        </div>

        <!-- اختبار وظائف التقارير -->
        <div class="test-section">
            <h2><i class="bi bi-file-earmark-text me-2"></i>وظائف التقارير</h2>
            <button class="test-button" onclick="testReportFunctions()">اختبار وظائف التقارير</button>
            <div id="reportResult" class="result"></div>
        </div>

        <!-- اختبار الوظائف الجديدة -->
        <div class="test-section">
            <h2><i class="bi bi-star me-2"></i>الوظائف الجديدة المضافة</h2>
            <button class="test-button" onclick="testNewFunctions()">اختبار الوظائف الجديدة</button>
            <div id="newResult" class="result"></div>
        </div>
    </div>

    <script src="js/app.js"></script>
    <script src="js/features.js"></script>
    <script src="js/reports.js"></script>
    <script>
        let testStats = {
            total: 0,
            passed: 0,
            failed: 0
        };

        // قوائم الوظائف للاختبار
        const functionGroups = {
            basic: ['showSection', 'logout', 'generateId'],
            product: ['showAddProductModal', 'generateBarcode', 'addProduct', 'editProduct', 'deleteProduct'],
            worker: ['showAddWorkerModal', 'addWorker', 'editWorker', 'deleteWorker'],
            attendance: ['markAllPresent', 'markAllLeave', 'printDailyAttendance', 'printWeeklyAttendance'],
            payroll: ['calculatePayroll', 'addNextMonth', 'addNextYear'],
            inventory: ['showAddStockModal', 'processStockOperation', 'addStock', 'removeStock'],
            report: ['exportToExcel', 'exportToPDF', 'generateProductReport'],
            new: ['updateArrivalTime', 'updateLeaveTime', 'markLeave', 'showSuccess', 'showError']
        };

        function testFunction(functionName, resultElementId) {
            try {
                if (typeof window[functionName] === 'function') {
                    document.getElementById(resultElementId).innerHTML += 
                        `<div class="success">✅ ${functionName}: موجود ويعمل</div>`;
                    testStats.passed++;
                    return true;
                } else if (typeof app[functionName] === 'function') {
                    document.getElementById(resultElementId).innerHTML += 
                        `<div class="success">✅ ${functionName}: موجود في app ويعمل</div>`;
                    testStats.passed++;
                    return true;
                } else {
                    document.getElementById(resultElementId).innerHTML += 
                        `<div class="error">❌ ${functionName}: غير موجود</div>`;
                    testStats.failed++;
                    return false;
                }
            } catch (error) {
                document.getElementById(resultElementId).innerHTML += 
                    `<div class="error">❌ ${functionName}: خطأ - ${error.message}</div>`;
                testStats.failed++;
                return false;
            } finally {
                testStats.total++;
                updateStats();
            }
        }

        function testBasicFunctions() {
            const resultElement = document.getElementById('basicResult');
            resultElement.innerHTML = '<h4>🔄 جاري اختبار الوظائف الأساسية...</h4>';
            
            functionGroups.basic.forEach(func => {
                testFunction(func, 'basicResult');
            });
        }

        function testProductFunctions() {
            const resultElement = document.getElementById('productResult');
            resultElement.innerHTML = '<h4>🔄 جاري اختبار وظائف المنتجات...</h4>';
            
            functionGroups.product.forEach(func => {
                testFunction(func, 'productResult');
            });
        }

        function testWorkerFunctions() {
            const resultElement = document.getElementById('workerResult');
            resultElement.innerHTML = '<h4>🔄 جاري اختبار وظائف العمال...</h4>';
            
            functionGroups.worker.forEach(func => {
                testFunction(func, 'workerResult');
            });
        }

        function testAttendanceFunctions() {
            const resultElement = document.getElementById('attendanceResult');
            resultElement.innerHTML = '<h4>🔄 جاري اختبار وظائف الحضور...</h4>';
            
            functionGroups.attendance.forEach(func => {
                testFunction(func, 'attendanceResult');
            });
        }

        function testPayrollFunctions() {
            const resultElement = document.getElementById('payrollResult');
            resultElement.innerHTML = '<h4>🔄 جاري اختبار وظائف المرتبات...</h4>';
            
            functionGroups.payroll.forEach(func => {
                testFunction(func, 'payrollResult');
            });
        }

        function testInventoryFunctions() {
            const resultElement = document.getElementById('inventoryResult');
            resultElement.innerHTML = '<h4>🔄 جاري اختبار وظائف المخزون...</h4>';
            
            functionGroups.inventory.forEach(func => {
                testFunction(func, 'inventoryResult');
            });
        }

        function testReportFunctions() {
            const resultElement = document.getElementById('reportResult');
            resultElement.innerHTML = '<h4>🔄 جاري اختبار وظائف التقارير...</h4>';
            
            functionGroups.report.forEach(func => {
                testFunction(func, 'reportResult');
            });
        }

        function testNewFunctions() {
            const resultElement = document.getElementById('newResult');
            resultElement.innerHTML = '<h4>🔄 جاري اختبار الوظائف الجديدة...</h4>';
            
            functionGroups.new.forEach(func => {
                testFunction(func, 'newResult');
            });
        }

        function runAllTests() {
            clearResults();
            testBasicFunctions();
            testProductFunctions();
            testWorkerFunctions();
            testAttendanceFunctions();
            testPayrollFunctions();
            testInventoryFunctions();
            testReportFunctions();
            testNewFunctions();
        }

        function clearResults() {
            testStats = { total: 0, passed: 0, failed: 0 };
            const resultElements = document.querySelectorAll('.result');
            resultElements.forEach(element => {
                element.innerHTML = '';
            });
            updateStats();
        }

        function updateStats() {
            document.getElementById('totalTests').textContent = testStats.total;
            document.getElementById('passedTests').textContent = testStats.passed;
            document.getElementById('failedTests').textContent = testStats.failed;
            
            const successRate = testStats.total > 0 ? 
                Math.round((testStats.passed / testStats.total) * 100) : 0;
            document.getElementById('successRate').textContent = successRate + '%';
        }

        // تشغيل اختبار أولي عند تحميل الصفحة
        window.onload = function() {
            updateStats();
        };
    </script>
</body>
</html>
